#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "soc/soc_caps.h"
#include "esp_log.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"
#include "adc_ntc.h"
#include "ntc_convert.h"

// ADC1 Channels
#define ADC1_CHAN0 ADC_CHANNEL_4

const static char *TAG = "NTC";

// 这里定义二维数组是为了支持多个ADC单元和多个通道的数据存储。
// 第一维 [2] ：支持2个ADC单元（ADC_UNIT_1 和 ADC_UNIT_2）
// 第二维 [5] ：每个单元支持最多5个通道
static int adc_raw[2][5];
static int voltage[2][5];

// adc1单元句柄(车间主任)
adc_oneshot_unit_handle_t adc1_handle;

// 校准句柄, 根据校准方案不同,句柄内部存储不同的校准数据(存储校准算法和参数)
adc_cali_handle_t adc1_cali_chan0_handle = NULL;

//
bool do_calibration1_chan0 = false;

// NTC温度传感器句柄 - 使用Type 0 (上拉电阻，Vref = Vs)
static ntc_handle_t ntc_handle;
static bool ntc_initialized = false;

// 尝试创建校准方案（优先Curve Fitting，其次Line Fitting）
// 返回是否校准成功 + 校准句柄
static bool adc_calibration_init(adc_unit_t unit, adc_channel_t channel, adc_atten_t atten, adc_cali_handle_t *out_handle)
{
    adc_cali_handle_t handle = NULL;
    esp_err_t ret = ESP_FAIL;
    bool calibrated = false;

#if ADC_CALI_SCHEME_CURVE_FITTING_SUPPORTED
    if (!calibrated)
    {
        ESP_LOGI(TAG, "calibration scheme version is %s", "Curve Fitting");
        adc_cali_curve_fitting_config_t cali_config = {
            .unit_id = unit,
            .chan = channel,
            .atten = atten,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        // 创建校准方案
        ret = adc_cali_create_scheme_curve_fitting(&cali_config, &handle);
        if (ret == ESP_OK)
        {
            calibrated = true;
        }
    }
#endif

#if ADC_CALI_SCHEME_LINE_FITTING_SUPPORTED
    if (!calibrated)
    {
        ESP_LOGI(TAG, "calibration scheme version is %s", "Line Fitting");
        adc_cali_line_fitting_config_t cali_config = {
            .unit_id = unit,
            .atten = atten,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        ret = adc_cali_create_scheme_line_fitting(&cali_config, &handle);
        if (ret == ESP_OK)
        {
            calibrated = true;
        }
    }
#endif

    *out_handle = handle;
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "Calibration Success");
    }
    else if (ret == ESP_ERR_NOT_SUPPORTED || !calibrated)
    {
        ESP_LOGW(TAG, "eFuse not burnt, skip software calibration");
    }
    else
    {
        ESP_LOGE(TAG, "Invalid arg or no memory");
    }

    return calibrated;
}

// 释放校准资源
static void adc_calibration_deinit(adc_cali_handle_t handle)
{
#if ADC_CALI_SCHEME_CURVE_FITTING_SUPPORTED
    ESP_LOGI(TAG, "deregister %s calibration scheme", "Curve Fitting");
    ESP_ERROR_CHECK(adc_cali_delete_scheme_curve_fitting(handle));

#elif ADC_CALI_SCHEME_LINE_FITTING_SUPPORTED
    ESP_LOGI(TAG, "deregister %s calibration scheme", "Line Fitting");
    ESP_ERROR_CHECK(adc_cali_delete_scheme_line_fitting(handle));
#endif
}

//
esp_err_t adc_ntc_init()
{
    esp_err_t ret;
    //
    adc_oneshot_unit_init_cfg_t init_config1 = {
        .unit_id = ADC_UNIT_1,
    };
    // 芯片可能会提供多个ADC单元,每个单元中又可有多个通道(相当于工厂有多间车间,每个车间有多个产线)
    // 此处代码声明(准备用(ADC_UNIT_1)车间,并任命一个车间主任(adc1_handle),有任何事情都找他)
    ret = adc_oneshot_new_unit(&init_config1, &adc1_handle);
    ESP_ERROR_CHECK(ret);

    // 配置生产线(ADC1_CHAN0)的参数
    adc_oneshot_chan_cfg_t config = {
        // ADC的分辩率
        // 让系统自动选择最高精度（通常是12位）
        .bitwidth = ADC_BITWIDTH_DEFAULT,

        // 设置最大衰减
        // 可以测量0-3.9V的电压范围
        .atten = ADC_ATTEN_DB_12,
    };
    // 总结: 设置 最高精度 + 最大测量范围
    ret = adc_oneshot_config_channel(adc1_handle, ADC1_CHAN0, &config);
    ESP_ERROR_CHECK(ret);

    // 进行软件校准,并返回校准句柄(存储校准算法和参数)
    do_calibration1_chan0 = adc_calibration_init(ADC_UNIT_1, ADC1_CHAN0, ADC_ATTEN_DB_12, &adc1_cali_chan0_handle);

    // 初始化NTC温度传感器 - 使用Type 0 (上拉电阻，Vref = Vs)
    ntc_config_t ntc_config;
    ret = ntc_convert_default_config(&ntc_config);
    if (ret == ESP_OK)
    {
        // 配置为Type 0: 上拉电阻，Vref = Vs
        ntc_config.type = NTC_TYPE_PULLUP_SAME_REF;
        ntc_config.max_adc = 4095; // 12位ADC
        ntc_config.rs = 10000;     // 10kΩ上拉电阻
        ntc_config.r25 = 10000;    // 25℃时10kΩ NTC
        ntc_config.b25 = 3950;     // B25值 (根据实际NTC规格书调整)
        ntc_config.vs = 3.3f;      // ESP32 3.3V电源
        ntc_config.vref = 3.3f;    // ADC参考电压3.3V

        ret = ntc_convert_init(&ntc_handle, &ntc_config);
        if (ret == ESP_OK)
        {
            ntc_initialized = true;
            ESP_LOGI(TAG, "NTC temperature sensor initialized successfully");
        }
        else
        {
            ESP_LOGE(TAG, "Failed to initialize NTC: %s", esp_err_to_name(ret));
        }
    }
    else
    {
        ESP_LOGE(TAG, "Failed to get NTC default config: %s", esp_err_to_name(ret));
    }

    return ret;
}

esp_err_t adc_ntc_read_chan0(int16_t *temp)
{
    esp_err_t ret;

    if (temp == NULL)
    {
        ESP_LOGE(TAG, "Temperature pointer is NULL");
        return ESP_ERR_INVALID_ARG;
    }

    // 读取ADC原始值
    ret = adc_oneshot_read(adc1_handle, ADC1_CHAN0, &adc_raw[0][0]);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "ADC read failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 校准ADC值为电压 (可选，用于调试)
    if (do_calibration1_chan0)
    {
        ret = adc_cali_raw_to_voltage(adc1_cali_chan0_handle, adc_raw[0][0], &voltage[0][0]);
        if (ret != ESP_OK)
        {
            ESP_LOGW(TAG, "ADC calibration failed: %s", esp_err_to_name(ret));
        }
    }

    // 使用NTC模块将ADC值转换为温度整数值
    if (ntc_initialized)
    {
        ret = ntc_convert_temperature(&ntc_handle, (uint16_t)adc_raw[0][0], temp);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "NTC temperature conversion failed: %s", esp_err_to_name(ret));
            *temp = 0; // 设置默认值
            return ret;
        }

        // 打印调试信息 (温度值已经是 x10 + 偏置格式)
        ESP_LOGI(TAG, "ADC Raw: %d, Voltage: %dmV, Temperature: %d (x10+200)",
                 adc_raw[0][0],
                 do_calibration1_chan0 ? voltage[0][0] : 0,
                 *temp);
    }
    else
    {
        ESP_LOGE(TAG, "NTC not initialized");
        *temp = 0;
        return ESP_ERR_INVALID_STATE;
    }

    return ESP_OK;
}
