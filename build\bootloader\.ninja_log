# ninja log v6
24	121	7784949837752313	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	b8ba88626490e6d2
43	230	7784949837952285	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	73d2ea14d681591b
28	245	7784949837802273	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	25e11b047edc83c0
19	261	7784949837712305	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	30f51d844e329496
55	269	7784949838072288	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	58f01bb682f2e3d7
49	277	7784949838012291	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	649cdad656f8b4f8
38	292	7784949837902314	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	937bd73ff42bf221
33	355	7784949837852327	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	d0886aec47ac34ed
61	425	7784949838134763	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	e1b94d12daf9ffd8
15	436	7784949837672275	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	31643bd8ceda0e35
81	444	7784949838324903	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	210943843935fd9b
89	452	7784949838414929	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	673ea9e469617ab
74	462	7784949838254925	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	5584ae294dc8bc2c
67	470	7784949838194907	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	cdfb41a536fb8bbd
9	477	7784949837612270	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	cb78176f3653dddc
113	606	7784949838644924	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	497155e73f89de4d
246	614	7784949839974941	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	ca32f6349de14497
262	628	7784949840134916	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	c0df74edbaa8d4e2
277	643	7784949840294915	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8a2798b39b38967
269	651	7784949840214951	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	4bd0cfcf61b4f9eb
97	682	7784949838484908	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	483192b6cbcd8ea8
105	708	7784949838564883	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	cfdd251618f6a714
124	722	7784949838764927	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	14c31d09cb487174
292	760	7784949840444938	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	83e187d5649e4953
231	779	7784949839824912	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	6aa3570ab1cd2ae2
436	865	7784949841884943	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4824227fe51f023e
477	896	7784949842294939	esp-idf/log/liblog.a	c91a251e1ec6f52c
426	941	7784949841774927	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	fda948f7d94318fe
628	950	7784949843804944	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	dcae97481a03c066
614	958	7784949843654933	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	83781b0edbf4dd06
356	1016	7784949841074922	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	63f8478ca2cd3234
462	1026	7784949842144951	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	4b64013a1931ce9e
643	1034	7784949843954947	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	fc218c3d43777ba7
709	1053	7784949844604936	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	b967448cdfaa7383
470	1061	7784949842214907	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	bf5475bb65ab941c
606	1068	7784949843574936	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	647702da2488015a
682	1076	7784949844344929	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	6790c1df7306deb2
452	1083	7784949842034919	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	858d4baa72e0a17b
444	1091	7784949841964941	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	142b08fee5111a88
651	1175	7784949844034939	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	70b39228ff3c3953
941	1303	7784949846924862	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	4ebae100feb37f9a
1016	1319	7784949847674911	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	abca8987946c4
896	1326	7784949846484895	esp-idf/esp_rom/libesp_rom.a	fa17a62755a86f30
1034	1348	7784949847864949	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	1b857d8603efa092
1061	1359	7784949848130066	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	6e27940ab0c3e7c
760	1366	7784949845124923	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	a0cddf8baf8a7725
779	1374	7784949845314963	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	ffdd27642721c0bf
1053	1382	7784949848044917	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	b80ca273a9dbb954
1083	1389	7784949848350239	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	25b380721e1b29b6
1027	1413	7784949847784913	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	889baa59c793157
1076	1442	7784949848280241	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	7337709c27fee1ca
958	1450	7784949847094932	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	233500e586f9ee34
722	1483	7784949844734946	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	606a76877fef8d81
1175	1521	7784949849270242	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	be38ffb92a960c1f
1091	1570	7784949848430242	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	35c22edb96f4c16a
1413	1578	7784949851650196	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	445508437b2f4d37
1382	1612	7784949851340237	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	24015bfc507f4dc0
1389	1623	7784949851410225	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	885af7f345ad7210
1069	1635	7784949848210224	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	749b6846e469439c
866	1664	7784949846174973	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	b77bc6b182498ad1
1303	1671	7784949850550202	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	28f496dae6971f53
1319	1681	7784949850710220	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	8f538409bc2cd6cc
1442	1699	7784949851940216	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	6d24e56d1ded1fb9
1366	1708	7784949851180213	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	3f7cd516dfa5aa97
1450	1716	7784949852020191	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	97f79caf25082d69
1484	1723	7784949852360233	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	1abbd206e65300d7
1522	1770	7784949852740209	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	29b8fde4d883d840
1326	1794	7784949850780232	esp-idf/esp_common/libesp_common.a	b03fb5144ef533f3
1570	1812	7784949853220252	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	754ba61bf0dd9e65
1348	1822	7784949851000238	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	61d58f48e37c570c
1359	1836	7784949851110224	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	a1902cbe249b4406
1612	1839	7784949853640259	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	2fbd10801eda5f93
1578	1840	7784949853300210	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	d3b413fd7717610e
1635	1840	7784949853870251	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	36a0b2d6be3ef1b
1623	1841	7784949853750248	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	9da01f23b4cebb82
1681	1841	7784949854330225	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	d880b4553289daf1
1664	1852	7784949854160210	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	58accd046d586db1
1771	1855	7784949855920246	project_elf_src_esp32c3.c	6c7d774e998bc721
1771	1855	7784949855920246	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/project_elf_src_esp32c3.c	6c7d774e998bc721
1671	1862	7784949854230220	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	e9fa507b2c68099a
950	1863	7784949847024879	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	7a3f3769d1ee15cd
1699	1867	7784949854510222	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	1af531e95af75350
1716	1867	7784949854680231	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	7ab51ff84ae785a9
1708	1871	7784949854600234	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	88ce9b6940f96e5c
1723	1900	7784949854750230	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	b9f0ed134b19d3fd
1855	1917	7784949856070257	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	fadd1928e73520b8
1794	1942	7784949855460232	esp-idf/esp_hw_support/libesp_hw_support.a	894c438beedadd28
1812	1952	7784949855640198	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	74d83039a25dbef3
1942	2017	7784949856943811	esp-idf/esp_system/libesp_system.a	4a8745f9c5d686d2
2017	2112	7784949857687218	esp-idf/efuse/libefuse.a	ee4f0e88cc352499
2113	2239	7784949858650550	esp-idf/bootloader_support/libbootloader_support.a	8733c75031909e5e
2239	2312	7784949859910515	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	dbdcbbb2f8f34b2
1374	2340	7784949851260240	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	2846994fd4688db
2312	2378	7784949860640535	esp-idf/spi_flash/libspi_flash.a	344553f8c1d7b7f4
2378	2453	7784949861300498	esp-idf/hal/libhal.a	38abfeb38a2a248d
2453	2522	7784949862050507	esp-idf/micro-ecc/libmicro-ecc.a	eed29c262133c2d0
2522	2630	7784949862740503	esp-idf/soc/libsoc.a	2ed56a05eac453da
2630	2693	7784949863820505	esp-idf/main/libmain.a	5bc613d7954c6145
2694	2812	7784949864450519	bootloader.elf	fced577b480f0e0d
2812	3091	7784949868386979	.bin_timestamp	f740b275d61ecfe
2812	3091	7784949868386979	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/.bin_timestamp	f740b275d61ecfe
3091	3205	7784949868426987	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
3091	3205	7784949868426987	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	c1b3e33447208055
